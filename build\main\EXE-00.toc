('E:\\git\\tvs\\tv_mqtt\\dist\\main.exe',
 True,
 False,
 False,
 ['E:\\git\\tvs\\tv_mqtt\\tv.ico'],
 None,
 True,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="requireAdministrator'
 b'" uiAccess="false"/>\n      </requestedPrivileges>\n    </security>\n  </tr'
 b'ustInfo>\n  <compatibility xmlns="urn:schemas-microsoft-com:compatibility'
 b'.v1">\n    <application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-'
 b'008deee3d3f0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a244022'
 b'5f93a}"/>\n      <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"'
 b'/>\n      <supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n    '
 b'  <supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </appli'
 b'cation>\n  </compatibility>\n  <application xmlns="urn:schemas-microsoft-c'
 b'om:asm.v3">\n    <windowsSettings>\n      <longPathAware xmlns="http://sch'
 b'emas.microsoft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </'
 b'windowsSettings>\n  </application>\n  <dependency>\n    <dependentAssembly>'
 b'\n      <assemblyIdentity type="win32" name="Microsoft.Windows.Common-Con'
 b'trols" version="6.0.0.0" processorArchitecture="*" publicKeyToken="6595b6414'
 b'4ccf1df" language="*"/>\n    </dependentAssembly>\n  </dependency>\n</assem'
 b'bly>',
 True,
 False,
 None,
 None,
 None,
 'E:\\git\\tvs\\tv_mqtt\\build\\main\\main.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', 'E:\\git\\tvs\\tv_mqtt\\build\\main\\PYZ-00.pyz', 'PYZ'),
  ('struct',
   'E:\\git\\tvs\\tv_mqtt\\build\\main\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\git\\tvs\\tv_mqtt\\build\\main\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\git\\tvs\\tv_mqtt\\build\\main\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\git\\tvs\\tv_mqtt\\build\\main\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\git\\tvs\\tv_mqtt\\build\\main\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('main', 'E:\\git\\tvs\\tv_mqtt\\main.py', 'PYSOURCE'),
  ('python310.dll', 'D:\\dev\\Python\\Python310\\python310.dll', 'BINARY'),
  ('select.pyd', 'D:\\dev\\Python\\Python310\\DLLs\\select.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\dev\\Python\\Python310\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\dev\\Python\\Python310\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\dev\\Python\\Python310\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\dev\\Python\\Python310\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\dev\\Python\\Python310\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\dev\\Python\\Python310\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd', 'D:\\dev\\Python\\Python310\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\dev\\Python\\Python310\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\dev\\Python\\Python310\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\dev\\Python\\Python310\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\dev\\Python\\Python310\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_uuid.pyd', 'D:\\dev\\Python\\Python310\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\dev\\Python\\Python310\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\dev\\Python\\Python310\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp310-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\yaml\\_yaml.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'D:\\dev\\Python\\Python310\\VCRUNTIME140.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'D:\\dev\\Python\\Python310\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'D:\\dev\\Python\\Python310\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libffi-7.dll', 'D:\\dev\\Python\\Python310\\DLLs\\libffi-7.dll', 'BINARY'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('base_library.zip',
   'E:\\git\\tvs\\tv_mqtt\\build\\main\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 **********,
 [('run.exe',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\run.exe',
   'EXECUTABLE')],
 'D:\\dev\\Python\\Python310\\python310.dll')
