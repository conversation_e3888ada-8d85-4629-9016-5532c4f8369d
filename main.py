import sys
import json
import tvms
import time
from datetime import datetime
from mqtt_client_ex import MqttClientEx
from mqtt_config import sys_config
import logging
import asyncio
import threading
import paho.mqtt.client as mqtt
import queue
import ctypes

# import debugpy


class MqttClientTv(MqttClientEx):
    def __init__(
        self,
        host: str,
        port: int,
        client_id: str = "",
        username=None,
        password=None,
        keepalive_interval: int = 10,
    ) -> None:
        rpc_callback_topic = None

        super().__init__(
            host,
            port,
            client_id,
            rpc_callback_topic,
            username,
            password,
            None,
            keepalive_interval,
            None,
        )

        self.topic_header = sys_config["MQTT"]["topic_header"]
        # 线程安全队列
        self._queue = queue.Queue()
        self._running_pub = True
        self._thread_pub = threading.Thread(
            target=self._wrap_pub_msg_loop, name="mqtt_message_publisher", daemon=True
        )
        self._thread_pub.start()

    def on_connect(self, client: mqtt.Client, userdata, flags, rc):
        # debugpy.debug_this_thread()
        logging.info("[mqtt] on_connect")

    def on_disconnect(self, client, userdata, msg):
        logging.info("[mqtt] on_disconnect")

    def on_message(self, client, userdata, msg):
        pass
        # logging.warning("[mqtt] unhandled msg [" + msg.topic + "]: " + str(msg.payload))

    def on_error(self, client, userdata, msg):
        logging.error(f"[mqtt-error] {msg}")

    def on_online(self, msg):
        data = json.loads(msg)
        logging.info(
            f"[mqtt-event] [online] id: {data['clientid']} name: {data['username']}"
        )

    def on_offline(self, msg):
        pass

    def publish_with_thread(self, topic, payload):
        # 放入线程安全队列
        self._queue.put((topic, payload))

    def pub_heartbeat(self):
        if self.is_connected:
            topic_heartbeat = f"{self.topic_header}/fishery_gateway/heartbeat"

            msg_heartbeat = {"msg": "heartbeat"}
            self.publish_with_thread(
                topic_heartbeat, json.dumps(msg_heartbeat, default=str)
            )

    def _wrap_pub_msg_loop(self):
        asyncio.run(self._pub_msg_loop())

    async def _pub_msg_loop(self):

        # debugpy.debug_this_thread()
        # 线程任务：从 Redis 队列中消费消息并发布到 MQTT
        while self._running_pub:
            # 使用 LPOP 阻塞式地从 Redis 队列获取请求
            try:
                topic, payload = self._queue.get()
                if isinstance(payload, dict):
                    payload = json.dumps(payload, default=str)
                self.publish(topic, payload)

            except Exception as e:
                logging.error(f"Error processing message: {e}")

    def stop_thread(self):
        """停止队列处理"""
        self._running_pub = False
        self._thread_pub.join()


mqtt_client = None


def tvms_status_callback(service_kind, ctxptr, message, status_code):
    """处理TVMS状态回调"""
    # debugpy.debug_this_thread()
    try:
        msg_str = message.data.decode("utf-8")

        print(f"\n=== TVMS回调数据 ===")
        print(f"服务类型: {service_kind}")
        print(f"状态码: {status_code}")

        global mqtt_client
        if mqtt_client:
            mqtt_client.publish_with_thread(
                f"{sys_config['MQTT']['topic_header']}/data/type{service_kind}", msg_str
            )

        # try:
        #     data = json.loads(msg_str)
        #     print("结构化数据:")
        #     print(json.dumps(data, indent=2, ensure_ascii=False))

        #     # 保存JSON到文件
        #     timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        #     filename = f"data_type{service_kind}_{timestamp}.json"

        #     with open(filename, 'w', encoding='utf-8') as f:
        #         json.dump(data, f, indent=2, ensure_ascii=False)
        #         print(f"数据已保存到: {filename}")

        # except json.JSONDecodeError:
        #     print(f"消息内容: {msg_str}")

    except Exception as e:
        print(f"回调处理异常: {str(e)}")


def main():

    # MQTT 配置
    mqtt_host = sys_config["MQTT"]["host"]
    mqtt_port = int(sys_config["MQTT"]["port"])
    mqtt_client_id = sys_config["MQTT"]["client_id"]
    mqtt_username = sys_config["MQTT"]["username"]
    mqtt_password = sys_config["MQTT"]["password"]
    topic_header = sys_config["MQTT"]["topic_header"]

    # MQTT 客户端
    global mqtt_client
    mqtt_client = MqttClientTv(
        mqtt_host, mqtt_port, mqtt_client_id, mqtt_username, mqtt_password
    )
    mqtt_client.connect()

    try:
        # 注册回调
        ctxptr = ctypes.c_void_p()  # 创建一个空的上下文指针
        callback = tvms.TvmsStatusCallback_t(tvms_status_callback)
        if not tvms.TvmsSetStatusCallback(callback, ctxptr):
            print("回调注册失败")
            return 1

        if not tvms.TvmsAcquisIsStarted():
            # 启动采集
            if not tvms.TvmsAcquisStart():
                print("启动采集失败")
                return 1

        print("TVMS运行中，等待数据... (Ctrl+C停止)")
        try:
            while True:
                time.sleep(0.5)

        except KeyboardInterrupt:
            print("\n正在停止...")

    except Exception as e:
        print(f"回调注册异常: {str(e)}")
        return 1

    finally:
        tvms.TvmsAcquisStop()
        print("采集已停止")


if __name__ == "__main__":
    sys.exit(main())
