'''
Descripttion: 
version: 0.x
Author: zhai
Date: 2025-07-20 16:08:22
LastEditors: zhai
LastEditTime: 2025-07-20 16:43:16
'''
import os
import sys
from typing import Any, Dict
import yaml

import sys
from pathlib import Path


def __GetBaseDir():
    if getattr(sys, "frozen", False):
        # 如果是被打包的应用程序，使用 sys.executable 获取路径
        root_path = Path(sys.executable).resolve()
    else:
        # 当前文件（config.py）的路径
        # root_path = Path(__file__).resolve().parent

        # 获取主脚本的路径(run.py)
        root_path = Path(sys.argv[0]).resolve()

    return root_path.parent


ROOT_PATH = __GetBaseDir()


def __load_sys_config(config_path: str = "config/sys_config.yaml") -> Dict[str, Any]:
    """加载系统配置文件"""

    default_config = {
        "MQTT": {
            "host": "************",
            "port": 1883,
            "client_id": "fishery_gateway",
            "username": "admin",
            "password": "emqx1234",
            "topic_header": "BHTSJ",
        },
        "OFFLINE": {
            "seconds": 60,
        },
    }

    # return default_config

    try:
        with open(config_path, "r", encoding="utf-8") as f:
            config = yaml.safe_load(f) or {}
        return config
    except Exception as e:
        print(f"加载系统配置失败，使用默认配置: {str(e)}")
        return default_config


def __get_sys_config():
    config_path = os.path.join(ROOT_PATH, "config/sys_config.yaml")

    print(f"config path: {config_path}")
    config = __load_sys_config(config_path)
    return config


sys_config = __get_sys_config()
