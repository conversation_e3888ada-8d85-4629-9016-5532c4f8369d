('E:\\git\\tvs\\tv_mqtt\\build\\main\\PYZ-00.pyz',
 [('__future__', 'D:\\dev\\Python\\Python310\\lib\\__future__.py', 'PYMODULE'),
  ('_aix_support',
   'D:\\dev\\Python\\Python310\\lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'D:\\dev\\Python\\Python310\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\dev\\Python\\Python310\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\dev\\Python\\Python310\\lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_osx_support',
   'D:\\dev\\Python\\Python310\\lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\dev\\Python\\Python310\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\dev\\Python\\Python310\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_sitebuiltins',
   'D:\\dev\\Python\\Python310\\lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime', 'D:\\dev\\Python\\Python310\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\dev\\Python\\Python310\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\dev\\Python\\Python310\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\dev\\Python\\Python310\\lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\dev\\Python\\Python310\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'D:\\dev\\Python\\Python310\\lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\dev\\Python\\Python310\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\dev\\Python\\Python310\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\dev\\Python\\Python310\\lib\\calendar.py', 'PYMODULE'),
  ('cgi', 'D:\\dev\\Python\\Python310\\lib\\cgi.py', 'PYMODULE'),
  ('concurrent',
   'D:\\dev\\Python\\Python310\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\dev\\Python\\Python310\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\dev\\Python\\Python310\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\dev\\Python\\Python310\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\dev\\Python\\Python310\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'D:\\dev\\Python\\Python310\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib', 'D:\\dev\\Python\\Python310\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars',
   'D:\\dev\\Python\\Python310\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\dev\\Python\\Python310\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\dev\\Python\\Python310\\lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\dev\\Python\\Python310\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\dev\\Python\\Python310\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\dev\\Python\\Python310\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\dev\\Python\\Python310\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\dev\\Python\\Python310\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\dev\\Python\\Python310\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\dev\\Python\\Python310\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\dev\\Python\\Python310\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\dev\\Python\\Python310\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\dev\\Python\\Python310\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'D:\\dev\\Python\\Python310\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\dev\\Python\\Python310\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\dev\\Python\\Python310\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\dev\\Python\\Python310\\lib\\dis.py', 'PYMODULE'),
  ('distutils',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.spawn',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.util',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'D:\\dev\\Python\\Python310\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('email', 'D:\\dev\\Python\\Python310\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\dev\\Python\\Python310\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\dev\\Python\\Python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\dev\\Python\\Python310\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\dev\\Python\\Python310\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\dev\\Python\\Python310\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\dev\\Python\\Python310\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\dev\\Python\\Python310\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\dev\\Python\\Python310\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\dev\\Python\\Python310\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\dev\\Python\\Python310\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\dev\\Python\\Python310\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\dev\\Python\\Python310\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\dev\\Python\\Python310\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\dev\\Python\\Python310\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\dev\\Python\\Python310\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\dev\\Python\\Python310\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\dev\\Python\\Python310\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\dev\\Python\\Python310\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\dev\\Python\\Python310\\lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\dev\\Python\\Python310\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\dev\\Python\\Python310\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\dev\\Python\\Python310\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\dev\\Python\\Python310\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\dev\\Python\\Python310\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\dev\\Python\\Python310\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\dev\\Python\\Python310\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\dev\\Python\\Python310\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\dev\\Python\\Python310\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\dev\\Python\\Python310\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\dev\\Python\\Python310\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\dev\\Python\\Python310\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'D:\\dev\\Python\\Python310\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'D:\\dev\\Python\\Python310\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\dev\\Python\\Python310\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'D:\\dev\\Python\\Python310\\lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'D:\\dev\\Python\\Python310\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\dev\\Python\\Python310\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._adapters',
   'D:\\dev\\Python\\Python310\\lib\\importlib\\_adapters.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\dev\\Python\\Python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\dev\\Python\\Python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   'D:\\dev\\Python\\Python310\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\dev\\Python\\Python310\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\dev\\Python\\Python310\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\dev\\Python\\Python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\dev\\Python\\Python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\dev\\Python\\Python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\dev\\Python\\Python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\dev\\Python\\Python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\dev\\Python\\Python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\dev\\Python\\Python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\dev\\Python\\Python310\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\dev\\Python\\Python310\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\dev\\Python\\Python310\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\dev\\Python\\Python310\\lib\\inspect.py', 'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('json', 'D:\\dev\\Python\\Python310\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'D:\\dev\\Python\\Python310\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\dev\\Python\\Python310\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\dev\\Python\\Python310\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'D:\\dev\\Python\\Python310\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'D:\\dev\\Python\\Python310\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\dev\\Python\\Python310\\lib\\mimetypes.py', 'PYMODULE'),
  ('mqtt_client_ex',
   'E:\\git\\tvs\\tv_mqtt\\mqtt_client_ex\\__init__.py',
   'PYMODULE'),
  ('mqtt_client_ex._mqtt_client_ex',
   'E:\\git\\tvs\\tv_mqtt\\mqtt_client_ex\\_mqtt_client_ex.py',
   'PYMODULE'),
  ('mqtt_config', 'E:\\git\\tvs\\tv_mqtt\\mqtt_config.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\dev\\Python\\Python310\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\dev\\Python\\Python310\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\dev\\Python\\Python310\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\dev\\Python\\Python310\\lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\dev\\Python\\Python310\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'D:\\dev\\Python\\Python310\\lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('paho',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\paho\\__init__.py',
   'PYMODULE'),
  ('paho.mqtt',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\paho\\mqtt\\__init__.py',
   'PYMODULE'),
  ('paho.mqtt.client',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\paho\\mqtt\\client.py',
   'PYMODULE'),
  ('paho.mqtt.enums',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\paho\\mqtt\\enums.py',
   'PYMODULE'),
  ('paho.mqtt.matcher',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\paho\\mqtt\\matcher.py',
   'PYMODULE'),
  ('paho.mqtt.packettypes',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\paho\\mqtt\\packettypes.py',
   'PYMODULE'),
  ('paho.mqtt.properties',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\paho\\mqtt\\properties.py',
   'PYMODULE'),
  ('paho.mqtt.reasoncodes',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\paho\\mqtt\\reasoncodes.py',
   'PYMODULE'),
  ('paho.mqtt.subscribeoptions',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\paho\\mqtt\\subscribeoptions.py',
   'PYMODULE'),
  ('pathlib', 'D:\\dev\\Python\\Python310\\lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'D:\\dev\\Python\\Python310\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\dev\\Python\\Python310\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\dev\\Python\\Python310\\lib\\platform.py', 'PYMODULE'),
  ('pprint', 'D:\\dev\\Python\\Python310\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'D:\\dev\\Python\\Python310\\lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'D:\\dev\\Python\\Python310\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\dev\\Python\\Python310\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\dev\\Python\\Python310\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('queue', 'D:\\dev\\Python\\Python310\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\dev\\Python\\Python310\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\dev\\Python\\Python310\\lib\\random.py', 'PYMODULE'),
  ('rlcompleter',
   'D:\\dev\\Python\\Python310\\lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'D:\\dev\\Python\\Python310\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\dev\\Python\\Python310\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\dev\\Python\\Python310\\lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.cygwin',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\cygwin.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.unix',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\unix.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.zos',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\zos.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.zosccompiler',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_distutils\\zosccompiler.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\dev\\Python\\Python310\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\dev\\Python\\Python310\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\dev\\Python\\Python310\\lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\dev\\Python\\Python310\\lib\\site.py', 'PYMODULE'),
  ('socket', 'D:\\dev\\Python\\Python310\\lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'D:\\dev\\Python\\Python310\\lib\\socketserver.py',
   'PYMODULE'),
  ('ssl', 'D:\\dev\\Python\\Python310\\lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\dev\\Python\\Python310\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\dev\\Python\\Python310\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\dev\\Python\\Python310\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\dev\\Python\\Python310\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\dev\\Python\\Python310\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\dev\\Python\\Python310\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\dev\\Python\\Python310\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\dev\\Python\\Python310\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\dev\\Python\\Python310\\lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\dev\\Python\\Python310\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\dev\\Python\\Python310\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\dev\\Python\\Python310\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'D:\\dev\\Python\\Python310\\lib\\tty.py', 'PYMODULE'),
  ('tvms', 'E:\\git\\tvs\\tv_mqtt\\tvms.py', 'PYMODULE'),
  ('typing', 'D:\\dev\\Python\\Python310\\lib\\typing.py', 'PYMODULE'),
  ('unittest',
   'D:\\dev\\Python\\Python310\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\dev\\Python\\Python310\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\dev\\Python\\Python310\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\dev\\Python\\Python310\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\dev\\Python\\Python310\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\dev\\Python\\Python310\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'D:\\dev\\Python\\Python310\\lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\dev\\Python\\Python310\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\dev\\Python\\Python310\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\dev\\Python\\Python310\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\dev\\Python\\Python310\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\dev\\Python\\Python310\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'D:\\dev\\Python\\Python310\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\dev\\Python\\Python310\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\dev\\Python\\Python310\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\dev\\Python\\Python310\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\dev\\Python\\Python310\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('uu', 'D:\\dev\\Python\\Python310\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'D:\\dev\\Python\\Python310\\lib\\uuid.py', 'PYMODULE'),
  ('uuid_extensions',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\uuid_extensions\\__init__.py',
   'PYMODULE'),
  ('uuid_extensions.uuid7',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\uuid_extensions\\uuid7.py',
   'PYMODULE'),
  ('webbrowser', 'D:\\dev\\Python\\Python310\\lib\\webbrowser.py', 'PYMODULE'),
  ('xml', 'D:\\dev\\Python\\Python310\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers',
   'D:\\dev\\Python\\Python310\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\dev\\Python\\Python310\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\dev\\Python\\Python310\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\dev\\Python\\Python310\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\dev\\Python\\Python310\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\dev\\Python\\Python310\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\dev\\Python\\Python310\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\dev\\Python\\Python310\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\dev\\Python\\Python310\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\dev\\Python\\Python310\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('yaml',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'C:\\Users\\<USER>\\AppData\\Local\\pypoetry\\Cache\\virtualenvs\\tv-mqtt-EBkXnLPJ-py3.10\\lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile', 'D:\\dev\\Python\\Python310\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\dev\\Python\\Python310\\lib\\zipimport.py', 'PYMODULE')])
